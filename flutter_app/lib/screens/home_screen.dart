import 'package:flutter/material.dart';
import 'standard_calculator_screen.dart';
import 'scientific_calculator_screen.dart';
import 'functions_screen.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    const StandardCalculatorScreen(),
    const ScientificCalculatorScreen(),
    const FunctionsScreen(),
    const SettingsScreen(),
  ];
  
  final List<String> _titles = [
    '标准',
    '科学',
    '功能',
    '设置',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_titles[_currentIndex]),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.calculate),
            label: '标准',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.functions),
            label: '科学',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.apps),
            label: '功能',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: '设置',
          ),
        ],
      ),
    );
  }
}
