import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/scientific_calculator_provider.dart';
import '../widgets/calculator_button.dart';
import '../widgets/calculator_display.dart';

class ScientificCalculatorScreen extends StatelessWidget {
  const ScientificCalculatorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ScientificCalculatorProvider>(
      builder: (context, calculator, child) {
        return Column(
          children: [
            // Display area (1/3 of screen)
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: CalculatorDisplay(
                  display: calculator.display,
                  expression: calculator.expression,
                ),
              ),
            ),
            
            // Button area (2/3 of screen) - 7行5列网格布局
            Expanded(
              flex: 2,
              child: Container(
                padding: const EdgeInsets.all(4),
                child: Column(
                  children: [
                    // 第一行: C、deg、fx、1/X、e
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'C',
                              onPressed: () => calculator.clear(),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: calculator.isDegreeMode ? 'deg' : 'rad',
                              onPressed: () => calculator.toggleAngleMode(),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'fx',
                              onPressed: () {}, // 预留功能
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '1/x',
                              onPressed: () => calculator.scientificFunction('reciprocal'),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'e',
                              onPressed: () => calculator.inputE(),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第二行: sin、√、xʸ、x!、π
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'sin',
                              onPressed: () => calculator.scientificFunction('sin'),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '√',
                              onPressed: () => calculator.scientificFunction('sqrt'),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'xʸ',
                              onPressed: () => calculator.inputPower(),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'x!',
                              onPressed: () => calculator.scientificFunction('factorial'),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'π',
                              onPressed: () => calculator.inputPi(),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第三行: cos、(、)、DEL、÷
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'cos',
                              onPressed: () => calculator.scientificFunction('cos'),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '(',
                              onPressed: () => calculator.inputOperator('('),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: ')',
                              onPressed: () => calculator.inputOperator(')'),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'DEL',
                              onPressed: () => calculator.delete(),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '÷',
                              onPressed: () => calculator.inputOperator('÷'),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第四行: tan、7、8、9、×
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'tan',
                              onPressed: () => calculator.scientificFunction('tan'),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '7',
                              onPressed: () => calculator.inputNumber('7'),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '8',
                              onPressed: () => calculator.inputNumber('8'),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '9',
                              onPressed: () => calculator.inputNumber('9'),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '×',
                              onPressed: () => calculator.inputOperator('×'),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第五行: cot、4、5、6、-
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'cot',
                              onPressed: () => calculator.scientificFunction('cot'),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '4',
                              onPressed: () => calculator.inputNumber('4'),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '5',
                              onPressed: () => calculator.inputNumber('5'),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '6',
                              onPressed: () => calculator.inputNumber('6'),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '-',
                              onPressed: () => calculator.inputOperator('-'),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第六行: ln、1、2、3、+
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'ln',
                              onPressed: () => calculator.scientificFunction('ln'),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '1',
                              onPressed: () => calculator.inputNumber('1'),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '2',
                              onPressed: () => calculator.inputNumber('2'),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '3',
                              onPressed: () => calculator.inputNumber('3'),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '+',
                              onPressed: () => calculator.inputOperator('+'),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第七行: lg、%、0、.、=
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'lg',
                              onPressed: () => calculator.scientificFunction('log'),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '%',
                              onPressed: () => calculator.percentage(),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '0',
                              onPressed: () => calculator.inputNumber('0'),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '.',
                              onPressed: () => calculator.inputDecimal(),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '=',
                              onPressed: () => calculator.calculate(),
                              backgroundColor: Colors.orange,
                              textColor: Colors.white,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
