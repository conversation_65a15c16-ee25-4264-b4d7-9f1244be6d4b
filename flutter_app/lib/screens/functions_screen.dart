import 'package:flutter/material.dart';
import 'functions/mortgage_calculator_screen.dart';
import 'functions/salary_calculator_screen.dart';
import 'functions/bmi_calculator_screen.dart';
import 'functions/insurance_calculator_screen.dart';
import 'functions/tax_calculator_screen.dart';
import 'functions/retirement_calculator_screen.dart';
import 'functions/currency_exchange_screen.dart';
import 'functions/relative_calculator_screen.dart';

class FunctionsScreen extends StatelessWidget {
  const FunctionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.count(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.1,
        children: [
          _buildFunctionCard(
            context,
            title: '房贷计算',
            icon: Icons.home,
            color: Colors.blue,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const MortgageCalculatorScreen()),
            ),
          ),
          _buildFunctionCard(
            context,
            title: '税后工资',
            icon: Icons.attach_money,
            color: Colors.green,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SalaryCalculatorScreen()),
            ),
          ),
          _buildFunctionCard(
            context,
            title: 'BMI计算',
            icon: Icons.fitness_center,
            color: Colors.orange,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const BMICalculatorScreen()),
            ),
          ),
          _buildFunctionCard(
            context,
            title: '五险一金',
            icon: Icons.security,
            color: Colors.purple,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const InsuranceCalculatorScreen()),
            ),
          ),
          _buildFunctionCard(
            context,
            title: '个税计算',
            icon: Icons.receipt,
            color: Colors.red,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const TaxCalculatorScreen()),
            ),
          ),
          _buildFunctionCard(
            context,
            title: '退休计算',
            icon: Icons.elderly,
            color: Colors.brown,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const RetirementCalculatorScreen()),
            ),
          ),
          _buildFunctionCard(
            context,
            title: '汇率换算',
            icon: Icons.currency_exchange,
            color: Colors.teal,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const CurrencyExchangeScreen()),
            ),
          ),
          _buildFunctionCard(
            context,
            title: '亲戚关系',
            icon: Icons.family_restroom,
            color: Colors.indigo,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const RelativeCalculatorScreen()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withOpacity(0.1),
                color.withOpacity(0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
