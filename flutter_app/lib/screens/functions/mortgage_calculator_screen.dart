import 'package:flutter/material.dart';
import 'dart:math' as math;

class MortgageCalculatorScreen extends StatefulWidget {
  const MortgageCalculatorScreen({super.key});

  @override
  State<MortgageCalculatorScreen> createState() => _MortgageCalculatorScreenState();
}

class _MortgageCalculatorScreenState extends State<MortgageCalculatorScreen> {
  final _loanAmountController = TextEditingController();
  final _interestRateController = TextEditingController();
  final _loanTermController = TextEditingController();
  
  double? _monthlyPayment;
  double? _totalPayment;
  double? _totalInterest;
  String _paymentType = '等额本息';

  @override
  void dispose() {
    _loanAmountController.dispose();
    _interestRateController.dispose();
    _loanTermController.dispose();
    super.dispose();
  }

  void _calculateMortgage() {
    final loanAmount = double.tryParse(_loanAmountController.text);
    final annualRate = double.tryParse(_interestRateController.text);
    final loanTermYears = double.tryParse(_loanTermController.text);

    if (loanAmount != null && annualRate != null && loanTermYears != null &&
        loanAmount > 0 && annualRate > 0 && loanTermYears > 0) {
      
      final monthlyRate = annualRate / 100 / 12;
      final totalMonths = loanTermYears * 12;

      if (_paymentType == '等额本息') {
        // 等额本息计算
        final monthlyPayment = loanAmount * 
            (monthlyRate * math.pow(1 + monthlyRate, totalMonths)) /
            (math.pow(1 + monthlyRate, totalMonths) - 1);
        
        setState(() {
          _monthlyPayment = monthlyPayment;
          _totalPayment = monthlyPayment * totalMonths;
          _totalInterest = _totalPayment! - loanAmount;
        });
      } else {
        // 等额本金计算
        final principalPayment = loanAmount / totalMonths;
        final firstMonthInterest = loanAmount * monthlyRate;
        final firstMonthPayment = principalPayment + firstMonthInterest;
        
        setState(() {
          _monthlyPayment = firstMonthPayment;
          _totalInterest = loanAmount * monthlyRate * (totalMonths + 1) / 2;
          _totalPayment = loanAmount + _totalInterest!;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('房贷计算器'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    TextField(
                      controller: _loanAmountController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: '贷款金额 (万元)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.attach_money),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _interestRateController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: '年利率 (%)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.percent),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _loanTermController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: '贷款年限',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _paymentType,
                      decoration: const InputDecoration(
                        labelText: '还款方式',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.payment),
                      ),
                      items: const [
                        DropdownMenuItem(value: '等额本息', child: Text('等额本息')),
                        DropdownMenuItem(value: '等额本金', child: Text('等额本金')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _paymentType = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: _calculateMortgage,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text(
                        '计算房贷',
                        style: TextStyle(fontSize: 18),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            if (_monthlyPayment != null) ...[
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            children: [
                              Text(
                                '计算结果',
                                style: Theme.of(context).textTheme.headlineSmall,
                              ),
                              const SizedBox(height: 20),
                              _buildResultItem(
                                '月供金额',
                                '¥${_monthlyPayment!.toStringAsFixed(2)}',
                                Colors.blue,
                              ),
                              const SizedBox(height: 12),
                              _buildResultItem(
                                '总还款额',
                                '¥${_totalPayment!.toStringAsFixed(2)}',
                                Colors.green,
                              ),
                              const SizedBox(height: 12),
                              _buildResultItem(
                                '总利息',
                                '¥${_totalInterest!.toStringAsFixed(2)}',
                                Colors.orange,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '还款说明',
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              const SizedBox(height: 12),
                              if (_paymentType == '等额本息') ...[
                                const Text('• 每月还款金额固定'),
                                const Text('• 前期利息占比较高，后期本金占比较高'),
                                const Text('• 适合收入稳定的借款人'),
                              ] else ...[
                                const Text('• 每月本金固定，利息递减'),
                                const Text('• 前期还款压力较大，后期逐渐减轻'),
                                const Text('• 总利息相对较少'),
                              ],
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 16),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
