import 'package:flutter/material.dart';

class TaxCalculatorScreen extends StatefulWidget {
  const TaxCalculatorScreen({super.key});

  @override
  State<TaxCalculatorScreen> createState() => _TaxCalculatorScreenState();
}

class _TaxCalculatorScreenState extends State<TaxCalculatorScreen> {
  final _monthlyIncomeController = TextEditingController();
  final _socialInsuranceController = TextEditingController();
  final _specialDeductionController = TextEditingController();
  final _additionalDeductionController = TextEditingController();

  String _taxType = '工资薪金';
  double? _monthlyTax;
  double? _yearlyTax;
  double? _afterTaxIncome;
  double? _taxableIncome;

  @override
  void dispose() {
    _monthlyIncomeController.dispose();
    _socialInsuranceController.dispose();
    _specialDeductionController.dispose();
    _additionalDeductionController.dispose();
    super.dispose();
  }

  void _calculateTax() {
    final monthlyIncome = double.tryParse(_monthlyIncomeController.text);
    final socialInsurance = double.tryParse(_socialInsuranceController.text) ?? 0;
    final specialDeduction = double.tryParse(_specialDeductionController.text) ?? 0;
    final additionalDeduction = double.tryParse(_additionalDeductionController.text) ?? 0;

    if (monthlyIncome != null && monthlyIncome > 0) {
      // 年收入
      final yearlyIncome = monthlyIncome * 12;

      // 计算应纳税所得额
      const basicDeduction = 60000.0; // 基本减除费用 5000*12
      final totalDeductions = socialInsurance * 12 + specialDeduction + additionalDeduction + basicDeduction;
      final taxableYearlyIncome = (yearlyIncome - totalDeductions).clamp(0.0, double.infinity);

      // 计算年度个税
      final yearlyTaxResult = _calculateYearlyTax(taxableYearlyIncome);

      setState(() {
        _taxableIncome = taxableYearlyIncome;
        _yearlyTax = yearlyTaxResult['tax'];
        _monthlyTax = _yearlyTax! / 12;
        _afterTaxIncome = monthlyIncome - _monthlyTax!;
      });
    }
  }

  Map<String, dynamic> _calculateYearlyTax(double taxableIncome) {
    List<Map<String, dynamic>> brackets = [
      {'min': 0, 'max': 36000, 'rate': 0.03, 'deduction': 0},
      {'min': 36000, 'max': 144000, 'rate': 0.10, 'deduction': 2520},
      {'min': 144000, 'max': 300000, 'rate': 0.20, 'deduction': 16920},
      {'min': 300000, 'max': 420000, 'rate': 0.25, 'deduction': 31920},
      {'min': 420000, 'max': 660000, 'rate': 0.30, 'deduction': 52920},
      {'min': 660000, 'max': 960000, 'rate': 0.35, 'deduction': 85920},
      {'min': 960000, 'max': double.infinity, 'rate': 0.45, 'deduction': 181920},
    ];

    double totalTax = 0;
    List<Map<String, dynamic>> breakdown = [];

    for (var bracket in brackets) {
      if (taxableIncome > bracket['min']) {
        double taxableInThisBracket = (taxableIncome - bracket['min']).clamp(0.0, bracket['max'] - bracket['min']).toDouble();
        if (taxableInThisBracket > 0) {
          double taxInThisBracket = taxableInThisBracket * bracket['rate'];
          totalTax += taxInThisBracket;

          breakdown.add({
            'range': '${_formatMoney(bracket['min'])} - ${bracket['max'] == double.infinity ? '以上' : _formatMoney(bracket['max'])}',
            'rate': '${(bracket['rate'] * 100).toStringAsFixed(0)}%',
            'taxableAmount': taxableInThisBracket,
            'tax': taxInThisBracket,
          });
        }
      }
    }

    // 使用速算扣除数方法验证
    for (var bracket in brackets.reversed) {
      if (taxableIncome > bracket['min']) {
        totalTax = taxableIncome * bracket['rate'] - bracket['deduction'];
        break;
      }
    }

    return {'tax': totalTax, 'breakdown': breakdown};
  }

  String _formatMoney(double amount) {
    if (amount >= 10000) {
      return '${(amount / 10000).toStringAsFixed(0)}万';
    }
    return amount.toStringAsFixed(0);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('个税计算器'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    DropdownButtonFormField<String>(
                      value: _taxType,
                      decoration: const InputDecoration(
                        labelText: '收入类型',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.work),
                      ),
                      items: const [
                        DropdownMenuItem(value: '工资薪金', child: Text('工资薪金所得')),
                        DropdownMenuItem(value: '劳务报酬', child: Text('劳务报酬所得')),
                        DropdownMenuItem(value: '稿酬', child: Text('稿酬所得')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _taxType = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _monthlyIncomeController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: '月收入 (元)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.attach_money),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _socialInsuranceController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: '月五险一金 (元)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.security),
                        hintText: '个人缴纳部分',
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _specialDeductionController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: '年专项附加扣除 (元)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.family_restroom),
                        hintText: '子女教育、住房贷款利息等',
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _additionalDeductionController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: '年其他扣除 (元)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.more_horiz),
                        hintText: '企业年金、商业健康保险等',
                      ),
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: _calculateTax,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text(
                        '计算个税',
                        style: TextStyle(fontSize: 18),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            if (_monthlyTax != null) ...[
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            children: [
                              Text(
                                '计算结果',
                                style: Theme.of(context).textTheme.headlineSmall,
                              ),
                              const SizedBox(height: 20),
                              _buildResultItem(
                                '月应纳税额',
                                '¥${_monthlyTax!.toStringAsFixed(2)}',
                                Colors.red,
                                isMain: true,
                              ),
                              const SizedBox(height: 12),
                              _buildResultItem(
                                '年应纳税额',
                                '¥${_yearlyTax!.toStringAsFixed(2)}',
                                Colors.orange,
                              ),
                              const SizedBox(height: 12),
                              _buildResultItem(
                                '税后月收入',
                                '¥${_afterTaxIncome!.toStringAsFixed(2)}',
                                Colors.green,
                              ),
                              const SizedBox(height: 12),
                              _buildResultItem(
                                '年应纳税所得额',
                                '¥${_taxableIncome!.toStringAsFixed(2)}',
                                Colors.blue,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(String label, String value, Color color, {bool isMain = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isMain ? 18 : 16,
            fontWeight: isMain ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isMain ? 24 : 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
