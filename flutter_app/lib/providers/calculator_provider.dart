import 'package:flutter/foundation.dart';
import 'package:math_expressions/math_expressions.dart';
import 'dart:math' as math;

class CalculatorProvider extends ChangeNotifier {
  String _display = '0';
  String _expression = '';
  bool _shouldResetDisplay = false;
  
  String get display => _display;
  String get expression => _expression;
  
  void inputNumber(String number) {
    if (_shouldResetDisplay) {
      _display = number;
      _expression = number;
      _shouldResetDisplay = false;
    } else {
      if (_display == '0') {
        _display = number;
        _expression = number;
      } else {
        _display += number;
        _expression += number;
      }
    }
    notifyListeners();
  }
  
  void inputOperator(String operator) {
    if (_shouldResetDisplay) {
      _shouldResetDisplay = false;
    }
    
    // Replace the last character if it's an operator
    if (_expression.isNotEmpty && _isOperator(_expression[_expression.length - 1])) {
      _expression = _expression.substring(0, _expression.length - 1) + operator;
    } else {
      _expression += operator;
    }
    
    _display = _expression;
    notifyListeners();
  }
  
  void inputDecimal() {
    if (_shouldResetDisplay) {
      _display = '0.';
      _expression = '0.';
      _shouldResetDisplay = false;
    } else {
      // Check if current number already has a decimal
      List<String> parts = _expression.split(RegExp(r'[+\-*/]'));
      String currentNumber = parts.last;
      
      if (!currentNumber.contains('.')) {
        if (_display == '0') {
          _display = '0.';
          _expression = '0.';
        } else {
          _display += '.';
          _expression += '.';
        }
      }
    }
    notifyListeners();
  }
  
  void calculate() {
    try {
      if (_expression.isEmpty) return;

      // Handle power operations first
      if (_expression.contains('^')) {
        _calculatePower(_expression);
        notifyListeners();
        return;
      }

      // Replace display operators with math expression operators
      String mathExpression = _expression
          .replaceAll('×', '*')
          .replaceAll('÷', '/');

      Parser parser = Parser();
      Expression exp = parser.parse(mathExpression);
      ContextModel cm = ContextModel();
      double result = exp.evaluate(EvaluationType.REAL, cm);

      // Format result
      if (result == result.toInt()) {
        _display = result.toInt().toString();
      } else {
        _display = result.toString();
      }

      _expression = _display;
      _shouldResetDisplay = true;
    } catch (e) {
      _display = 'Error';
      _expression = '';
      _shouldResetDisplay = true;
    }
    notifyListeners();
  }
  
  void clear() {
    _display = '0';
    _expression = '';
    _shouldResetDisplay = false;
    notifyListeners();
  }
  
  void delete() {
    if (_shouldResetDisplay) {
      clear();
      return;
    }
    
    if (_expression.isNotEmpty) {
      _expression = _expression.substring(0, _expression.length - 1);
      if (_expression.isEmpty) {
        _display = '0';
      } else {
        _display = _expression;
      }
    }
    notifyListeners();
  }
  
  void toggleSign() {
    if (_display == '0' || _display == 'Error') return;
    
    if (_display.startsWith('-')) {
      _display = _display.substring(1);
      _expression = _expression.substring(1);
    } else {
      _display = '-$_display';
      _expression = '-$_expression';
    }
    notifyListeners();
  }
  
  void percentage() {
    try {
      double value = double.parse(_display);
      double result = value / 100;
      
      if (result == result.toInt()) {
        _display = result.toInt().toString();
      } else {
        _display = result.toString();
      }
      
      _expression = _display;
      _shouldResetDisplay = true;
    } catch (e) {
      _display = 'Error';
      _expression = '';
      _shouldResetDisplay = true;
    }
    notifyListeners();
  }
  
  // Scientific calculator functions
  void scientificFunction(String function) {
    try {
      double value = double.parse(_display);
      double result;

      switch (function) {
        case 'sin':
          result = math.sin(value * (math.pi / 180)); // Convert to radians
          break;
        case 'cos':
          result = math.cos(value * (math.pi / 180));
          break;
        case 'tan':
          result = math.tan(value * (math.pi / 180));
          break;
        case 'log':
          result = math.log(value) / math.ln10;
          break;
        case 'ln':
          result = math.log(value);
          break;
        case 'sqrt':
          result = math.sqrt(value);
          break;
        case 'square':
          result = value * value;
          break;
        case 'cube':
          result = value * value * value;
          break;
        case 'reciprocal':
          result = 1 / value;
          break;
        case 'factorial':
          result = _factorial(value.toInt()).toDouble();
          break;
        default:
          result = value;
      }

      if (result == result.toInt()) {
        _display = result.toInt().toString();
      } else {
        _display = result.toString();
      }

      _expression = _display;
      _shouldResetDisplay = true;
    } catch (e) {
      _display = 'Error';
      _expression = '';
      _shouldResetDisplay = true;
    }
    notifyListeners();
  }

  // Power function (x^y)
  void inputPower() {
    if (_shouldResetDisplay) {
      _shouldResetDisplay = false;
    }

    // Replace the last character if it's an operator
    if (_expression.isNotEmpty && _isOperator(_expression[_expression.length - 1])) {
      _expression = '${_expression.substring(0, _expression.length - 1)}^';
    } else {
      _expression += '^';
    }

    _display = _expression;
    notifyListeners();
  }

  // Calculate power when equals is pressed
  void _calculatePower(String expression) {
    if (expression.contains('^')) {
      List<String> parts = expression.split('^');
      if (parts.length == 2) {
        try {
          double base = double.parse(parts[0]);
          double exponent = double.parse(parts[1]);
          double result = math.pow(base, exponent).toDouble();

          if (result == result.toInt()) {
            _display = result.toInt().toString();
          } else {
            _display = result.toString();
          }

          _expression = _display;
          _shouldResetDisplay = true;
          return;
        } catch (e) {
          _display = 'Error';
          _expression = '';
          _shouldResetDisplay = true;
          return;
        }
      }
    }
  }
  
  void inputPi() {
    if (_shouldResetDisplay) {
      _display = math.pi.toString();
      _expression = math.pi.toString();
      _shouldResetDisplay = false;
    } else {
      if (_display == '0') {
        _display = math.pi.toString();
        _expression = math.pi.toString();
      } else {
        _display += math.pi.toString();
        _expression += math.pi.toString();
      }
    }
    notifyListeners();
  }
  
  bool _isOperator(String char) {
    return ['+', '-', '×', '÷', '*', '/', '^'].contains(char);
  }
  
  int _factorial(int n) {
    if (n <= 1) return 1;
    return n * _factorial(n - 1);
  }
}
